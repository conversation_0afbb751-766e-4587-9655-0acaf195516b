/* 太空主题样式文件 */

/* 太空背景类 */
.space-background {
    position: relative;
    overflow: hidden;
}

/* 主要内容区域 */
.main-content {
    position: relative;
    z-index: 1;
}

/* 星星背景层 */
.stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -3;
    background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="10" cy="10" r="0.5" fill="white" opacity="0.8"/><circle cx="30" cy="25" r="0.3" fill="white" opacity="0.6"/><circle cx="60" cy="15" r="0.4" fill="white" opacity="0.9"/><circle cx="80" cy="35" r="0.2" fill="white" opacity="0.7"/><circle cx="20" cy="50" r="0.3" fill="white" opacity="0.5"/><circle cx="70" cy="60" r="0.5" fill="white" opacity="0.8"/><circle cx="40" cy="80" r="0.2" fill="white" opacity="0.6"/><circle cx="90" cy="70" r="0.4" fill="white" opacity="0.9"/></svg>') repeat;
    background-size: 200px 200px;
    animation: move-stars 50s linear infinite;
}

/* 闪烁星星层 */
.twinkling {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="15" cy="20" r="0.3" fill="white"><animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/></circle><circle cx="45" cy="40" r="0.2" fill="white"><animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/></circle><circle cx="75" cy="30" r="0.4" fill="white"><animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/></circle><circle cx="25" cy="70" r="0.2" fill="white"><animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/></circle><circle cx="85" cy="80" r="0.3" fill="white"><animate attributeName="opacity" values="0;1;0" dur="3.5s" repeatCount="indefinite"/></circle></svg>') repeat;
    background-size: 300px 300px;
    animation: move-twinkling 100s linear infinite;
}

/* 云层效果 */
.clouds {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: transparent url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><ellipse cx="20" cy="30" rx="8" ry="3" fill="rgba(255,255,255,0.1)"/><ellipse cx="60" cy="20" rx="12" ry="4" fill="rgba(255,255,255,0.08)"/><ellipse cx="80" cy="60" rx="10" ry="3" fill="rgba(255,255,255,0.12)"/><ellipse cx="30" cy="80" rx="15" ry="5" fill="rgba(255,255,255,0.06)"/></svg>') repeat;
    background-size: 400px 400px;
    animation: move-clouds 200s linear infinite;
}

/* 动画定义 */
@keyframes move-stars {
    from { transform: translateX(0); }
    to { transform: translateX(-200px); }
}

@keyframes move-twinkling {
    from { transform: translateX(0); }
    to { transform: translateX(-300px); }
}

@keyframes move-clouds {
    from { transform: translateX(0); }
    to { transform: translateX(-400px); }
}

/* 加载动画样式 */
#app-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #533483 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.app-loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(83, 109, 254, 0.3);
    border-top: 4px solid #536dfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.app-loading-text {
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(83, 109, 254, 0.8);
}

.app-loading-progress {
    width: 300px;
    height: 4px;
    background-color: rgba(83, 109, 254, 0.3);
    border-radius: 2px;
    overflow: hidden;
}

.app-loading-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #536dfe, #3f51b5, #536dfe);
    background-size: 200% 100%;
    animation: loading-progress 2s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes loading-progress {
    0% { width: 0%; background-position: 200% 0; }
    50% { width: 100%; background-position: 0% 0; }
    100% { width: 100%; background-position: -200% 0; }
}

/* 隐藏加载界面 */
.app-loaded #app-loading-overlay {
    opacity: 0;
    pointer-events: none;
}

/* 增强卡片发光效果 */
.space-card {
    position: relative;
    transition: all 0.3s ease;
}

.space-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, transparent, rgba(83, 109, 254, 0.4), transparent);
    border-radius: 12px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.space-card:hover::before {
    opacity: 1;
}

.space-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 40px rgba(83, 109, 254, 0.4);
}

/* 按钮发光效果 */
.space-btn-primary {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.space-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.space-btn-primary:hover::before {
    left: 100%;
}

.space-btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 20px rgba(83, 109, 254, 0.5);
}

/* 输入框发光效果 */
.space-input {
    transition: all 0.3s ease;
    position: relative;
}

.space-input:focus {
    transform: scale(1.02);
    box-shadow: 0 0 20px rgba(83, 109, 254, 0.4);
}

/* 徽章脉冲效果 */
.space-badge {
    animation: badge-pulse 2s ease-in-out infinite;
}

@keyframes badge-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* 标题发光效果 */
.space-title {
    animation: title-glow 3s ease-in-out infinite alternate;
}

@keyframes title-glow {
    from { text-shadow: 0 0 10px rgba(83, 109, 254, 0.8); }
    to { text-shadow: 0 0 20px rgba(83, 109, 254, 1), 0 0 30px rgba(83, 109, 254, 0.8); }
}
