body {
    font-family: 'Arial', sans-serif;
    color: #fff;
    min-height: 100vh;
    overflow-x: hidden;
    background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
    position: relative;
}

/* 添加星空背景动画 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
    z-index: -1;
    opacity: 0.8;
}

@keyframes sparkle {
    from { transform: translateX(0); }
    to { transform: translateX(-200px); }
}

.space-card {
    background: linear-gradient(145deg, rgba(25, 32, 71, 0.9), rgba(37, 45, 90, 0.8));
    border: 1px solid rgba(83, 109, 254, 0.6);
    border-radius: 15px;
    box-shadow:
        0 8px 32px rgba(83, 109, 254, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    position: relative;
    transition: all 0.3s ease;
}

.space-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 15px;
    padding: 1px;
    background: linear-gradient(45deg, transparent, rgba(83, 109, 254, 0.5), transparent);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.space-card:hover::after {
    opacity: 1;
}

.space-card-header {
    background-color: rgba(37, 45, 90, 0.8);
    border-bottom: 1px solid rgba(83, 109, 254, 0.5);
    color: #fff;
}

.space-title {
    color: #fff;
    text-shadow: 0 0 10px rgba(83, 109, 254, 0.8);
    font-weight: bold;
}

.space-input {
    background-color: rgba(25, 32, 71, 0.6);
    border: 1px solid rgba(83, 109, 254, 0.5);
    color: #fff;
}

.space-input:focus {
    background-color: rgba(37, 45, 90, 0.8);
    border-color: rgba(83, 109, 254, 0.8);
    box-shadow: 0 0 0 0.25rem rgba(83, 109, 254, 0.25);
    color: #fff;
}

.space-btn {
    border-color: rgba(83, 109, 254, 0.5);
    color: rgba(83, 109, 254, 1);
}

.space-btn:hover {
    background-color: rgba(83, 109, 254, 0.2);
    border-color: rgba(83, 109, 254, 0.8);
    color: #fff;
}

.space-btn-primary {
    background: linear-gradient(145deg, rgba(83, 109, 254, 0.8), rgba(63, 81, 181, 0.9));
    border: 1px solid rgba(83, 109, 254, 0.6);
    color: #fff;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(83, 109, 254, 0.3);
}

.space-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.space-btn-primary:hover {
    background: linear-gradient(145deg, rgba(83, 109, 254, 1), rgba(63, 81, 181, 1));
    border-color: rgba(83, 109, 254, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(83, 109, 254, 0.5);
}

.space-btn-primary:hover::before {
    left: 100%;
}

.space-btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(83, 109, 254, 0.3);
}

.space-badge {
    background: linear-gradient(145deg, rgba(37, 45, 90, 0.9), rgba(25, 32, 71, 0.8));
    border: 1px solid rgba(83, 109, 254, 0.4);
    font-size: 0.85rem;
    padding: 0.5rem 0.8rem;
    color: #4dabf7 !important;
    font-weight: bold;
    border-radius: 20px;
    box-shadow: 0 2px 10px rgba(83, 109, 254, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.space-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(77, 171, 247, 0.1), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.space-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(83, 109, 254, 0.3);
}

.space-badge:hover::before {
    opacity: 1;
}

.typing-container {
    position: relative;
}

.text-display {
    background: linear-gradient(145deg, rgba(37, 45, 90, 0.6), rgba(25, 32, 71, 0.5));
    border: 1px solid rgba(83, 109, 254, 0.3);
    border-radius: 12px;
    padding: 25px;
    font-size: 1.5rem;
    line-height: 1.8;
    height: 200px;  /* 固定高度 */
    margin-bottom: 20px;
    font-family: 'Courier New', monospace;
    position: relative;
    overflow: hidden;  /* 防止内容溢出 */
    box-shadow:
        inset 0 2px 10px rgba(0, 0, 0, 0.3),
        0 4px 20px rgba(83, 109, 254, 0.1);
    transition: all 0.3s ease;
}

.text-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(83, 109, 254, 0.05), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.text-display:hover::before {
    opacity: 1;
}

.text-display .correct {
    color: #00ff88;
    text-shadow: 0 0 8px rgba(0, 255, 136, 0.6);
    animation: correct-glow 0.3s ease-out;
}

.text-display .incorrect {
    color: #ff4757;
    text-shadow: 0 0 8px rgba(255, 71, 87, 0.6);
    background-color: rgba(255, 71, 87, 0.1);
    animation: incorrect-shake 0.3s ease-out;
}

.text-display .current {
    background: linear-gradient(90deg, rgba(83, 109, 254, 0.4), rgba(63, 81, 181, 0.3));
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(83, 109, 254, 0.5);
    animation: current-pulse 1.5s ease-in-out infinite;
}

@keyframes correct-glow {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes incorrect-shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

@keyframes current-pulse {
    0%, 100% {
        background: linear-gradient(90deg, rgba(83, 109, 254, 0.4), rgba(63, 81, 181, 0.3));
        box-shadow: 0 0 10px rgba(83, 109, 254, 0.5);
    }
    50% {
        background: linear-gradient(90deg, rgba(83, 109, 254, 0.6), rgba(63, 81, 181, 0.5));
        box-shadow: 0 0 15px rgba(83, 109, 254, 0.8);
    }
}

.text-display small {
    font-size: 0.9rem;
    display: block;
    margin-top: 10px;
    color: #adb5bd;
}

.input-container {
    margin-top: 20px;
}

.typing-container .input-container {
    margin-top: 20px;
}

.typing-container .space-input {
    height: 100px;  /* 减小输入框高度 */
    font-size: 1.5rem;
    line-height: 1.8;
    padding: 15px;
    resize: none;
}

.status-message {
    font-size: 0.9rem;
    color: #adb5bd;
    text-align: center;
}

.rocket-animation {
    animation: rocket-float 3s ease-in-out infinite;
}

.rocket {
    width: 100px;
    height: auto;
}

@keyframes rocket-float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-15px);
    }
    100% {
        transform: translateY(0);
    }
}

.medal {
    width: 120px;
    height: auto;
    animation: medal-shine 2s ease-in-out infinite;
}

@keyframes medal-shine {
    0% {
        filter: brightness(1);
    }
    50% {
        filter: brightness(1.3);
    }
    100% {
        filter: brightness(1);
    }
}

.waiting-text {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.user-count {
    font-size: 1rem;
    color: #adb5bd;
}

table.table-dark {
    background-color: transparent;
}

table.table-dark thead th {
    background-color: rgba(37, 45, 90, 0.8);
    border-color: rgba(83, 109, 254, 0.3);
}

table.table-dark tbody td {
    background-color: rgba(25, 32, 71, 0.5);
    border-color: rgba(83, 109, 254, 0.2);
}

table.table-dark tbody tr:hover td {
    background-color: rgba(37, 45, 90, 0.7);
}

/* 昵称显示样式 */
#nickname {
    color: #3498db;
    font-weight: bold;
}

/* 添加结果界面的样式 */
.result-stats {
    font-size: 1.5rem;
    margin: 20px 0;
    padding: 20px;
    background-color: rgba(37, 45, 90, 0.5);
    border-radius: 10px;
}

.result-stats p {
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
    padding: 0 50px;
}

.result-stats span {
    font-weight: bold;
    color: #3498db;
}

/* 修改文本显示区域宽度 */
.col-md-8 {
    width: 70%;  /* 增加主要内容区域的宽度 */
}

.col-md-4 {
    width: 30%;  /* 减小排行榜区域的宽度 */
}

.current-line {
    background-color: rgba(83, 109, 254, 0.3);  /* 增加背景色透明度 */
    padding: 8px;  /* 增加内边距 */
    border-radius: 5px;
    margin-bottom: 10px;
    border-left: 4px solid #3498db;  /* 添加左边框 */
    box-shadow: 0 0 5px rgba(83, 109, 254, 0.5);  /* 添加发光效果 */
}

.other-line {
    color: #95a5a6;  /* 调整其他行的颜色 */
    margin-bottom: 10px;
    padding: 8px;
    opacity: 0.7;  /* 降低其他行的不透明度 */
}

.page-info {
    position: absolute;
    bottom: 10px;
    right: 20px;
    font-size: 0.8rem;
    color: #95a5a6;
}

/* 修改结果界面的样式 */
#result-screen .card-body {
    padding: 30px;
}

#result-screen .result-stats {
    background-color: rgba(37, 45, 90, 0.5);
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

#result-screen .result-stats p {
    display: flex;
    justify-content: space-between;
    margin: 10px 0;
    font-size: 1.2rem;
}

#result-screen .result-stats span {
    color: #3498db;
    font-weight: bold;
}

#result-screen .medal {
    width: 120px;
    height: auto;
    margin: 0 auto;
    display: block;
}

/* 结果界面的排行榜样式 */
#result-screen .table {
    margin-bottom: 0;
}

#result-screen .table th,
#result-screen .table td {
    padding: 12px;
    text-align: center;
}

/* 添加昵称显示样式 */
.user-nickname-display {
    font-size: 1.25rem;
    font-weight: 600;
    color: #4dabf7;
    text-shadow: 0 0 10px rgba(77, 171, 247, 0.5);
    background-color: rgba(0, 0, 0, 0.3);
    padding: 8px 15px;
    border-radius: 10px;
    border: 1px solid rgba(77, 171, 247, 0.3);
    display: inline-block;
    margin-bottom: 15px;
}

/* 排行榜中的昵称样式 */
.leaderboard-nickname {
    font-weight: 600;
    color: #4dabf7;
}

/* 完成后的消息样式 */
.completed-message {
    color: #28a745;
    font-size: 1.5rem;
    text-align: center;
    padding: 20px;
    margin-top: 20px;
    text-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

.waiting-message {
    margin-top: 15px;
    text-align: center;
    font-weight: 500;
}

.waiting-message .spinner-border {
    margin-left: 10px;
}

/* 虚拟键盘增强样式 */
.virtual-keyboard .key {
    background: linear-gradient(145deg, rgba(37, 45, 90, 0.8), rgba(25, 32, 71, 0.9));
    border: 1px solid rgba(83, 109, 254, 0.4);
    color: #ffffff;
    border-radius: 8px;
    margin: 2px;
    padding: 8px 12px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
}

.virtual-keyboard .key::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.virtual-keyboard .key:hover {
    background: linear-gradient(145deg, rgba(83, 109, 254, 0.6), rgba(63, 81, 181, 0.7));
    border-color: rgba(83, 109, 254, 0.8);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(83, 109, 254, 0.4);
}

.virtual-keyboard .key:hover::before {
    left: 100%;
}

.virtual-keyboard .key.active,
.virtual-keyboard .key.glow {
    background: linear-gradient(145deg, rgba(83, 109, 254, 0.9), rgba(63, 81, 181, 1));
    border-color: rgba(83, 109, 254, 1);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(83, 109, 254, 0.6);
    animation: key-press 0.3s ease-out;
}

.virtual-keyboard .key.space {
    min-width: 200px;
    background: linear-gradient(145deg, rgba(37, 45, 90, 0.8), rgba(25, 32, 71, 0.9));
}

.virtual-keyboard .key.space:hover {
    background: linear-gradient(145deg, rgba(83, 109, 254, 0.6), rgba(63, 81, 181, 0.7));
}

.virtual-keyboard .key.space.active,
.virtual-keyboard .key.space.glow {
    background: linear-gradient(145deg, rgba(83, 109, 254, 0.9), rgba(63, 81, 181, 1));
}

@keyframes key-press {
    0% { transform: translateY(-2px) scale(1); }
    50% { transform: translateY(-3px) scale(1.05); }
    100% { transform: translateY(-2px) scale(1); }
}

/* 排行榜增强样式 */
.leaderboard-table {
    background: linear-gradient(145deg, rgba(25, 32, 71, 0.9), rgba(37, 45, 90, 0.8));
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(83, 109, 254, 0.3);
}

.leaderboard-table thead th {
    background: linear-gradient(145deg, rgba(83, 109, 254, 0.8), rgba(63, 81, 181, 0.9));
    color: #ffffff;
    font-weight: bold;
    text-align: center;
    padding: 15px 10px;
    border: none;
}

.leaderboard-table tbody tr {
    transition: all 0.3s ease;
}

.leaderboard-table tbody tr:hover {
    background: linear-gradient(145deg, rgba(83, 109, 254, 0.2), rgba(63, 81, 181, 0.1));
    transform: translateX(5px);
}

.leaderboard-table tbody td {
    padding: 12px 10px;
    text-align: center;
    border: none;
    color: #ffffff;
}

/* 排名徽章样式 */
.rank-badge {
    display: inline-block;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    line-height: 30px;
    text-align: center;
    font-weight: bold;
    color: #ffffff;
}

.rank-badge.rank-1 {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    color: #333;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
}

.rank-badge.rank-2 {
    background: linear-gradient(145deg, #c0c0c0, #e5e5e5);
    color: #333;
    box-shadow: 0 0 15px rgba(192, 192, 192, 0.6);
}

.rank-badge.rank-3 {
    background: linear-gradient(145deg, #cd7f32, #daa520);
    color: #fff;
    box-shadow: 0 0 15px rgba(205, 127, 50, 0.6);
}

/* 通知样式增强 */
.notification {
    background: linear-gradient(145deg, rgba(25, 32, 71, 0.95), rgba(37, 45, 90, 0.9));
    border: 1px solid rgba(83, 109, 254, 0.6);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(83, 109, 254, 0.4);
    backdrop-filter: blur(15px);
}

/* 输入框聚焦时的特效 */
.space-input:focus {
    animation: input-glow 2s ease-in-out infinite alternate;
}

@keyframes input-glow {
    from { box-shadow: 0 0 20px rgba(83, 109, 254, 0.4); }
    to { box-shadow: 0 0 30px rgba(83, 109, 254, 0.8), 0 0 40px rgba(83, 109, 254, 0.4); }
}

/* 前三名行的特殊样式 */
.rank-1-row {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.1), rgba(255, 237, 78, 0.05)) !important;
    border-left: 4px solid #ffd700;
}

.rank-2-row {
    background: linear-gradient(145deg, rgba(192, 192, 192, 0.1), rgba(229, 229, 229, 0.05)) !important;
    border-left: 4px solid #c0c0c0;
}

.rank-3-row {
    background: linear-gradient(145deg, rgba(205, 127, 50, 0.1), rgba(218, 165, 32, 0.05)) !important;
    border-left: 4px solid #cd7f32;
}

.rank-1-row:hover,
.rank-2-row:hover,
.rank-3-row:hover {
    transform: translateX(8px) !important;
    box-shadow: 0 4px 20px rgba(83, 109, 254, 0.3);
}

/* 通用排名徽章样式 */
.rank-badge {
    background: linear-gradient(145deg, rgba(83, 109, 254, 0.8), rgba(63, 81, 181, 0.9));
    color: #ffffff;
}

/* 加载动画增强 */
.spinner-border {
    border-color: rgba(83, 109, 254, 0.3);
    border-top-color: #536dfe;
}

/* 等待界面美化 */
.waiting-text {
    text-shadow: 0 0 10px rgba(83, 109, 254, 0.8);
    animation: waiting-pulse 2s ease-in-out infinite alternate;
}

@keyframes waiting-pulse {
    from { opacity: 0.8; }
    to { opacity: 1; }
}

/* 用户计数样式 */
.user-count {
    background: linear-gradient(145deg, rgba(37, 45, 90, 0.8), rgba(25, 32, 71, 0.9));
    padding: 8px 15px;
    border-radius: 20px;
    border: 1px solid rgba(83, 109, 254, 0.3);
    display: inline-block;
    margin-bottom: 10px;
}

/* 结果界面动画 */
.result-animation {
    animation: result-celebration 2s ease-in-out infinite alternate;
}

@keyframes result-celebration {
    from { transform: scale(1) rotate(0deg); }
    to { transform: scale(1.05) rotate(5deg); }
}