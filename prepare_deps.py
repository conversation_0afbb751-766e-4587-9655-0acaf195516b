import os
import requests
import shutil

# 创建lib目录
lib_dir = os.path.join('client', 'lib')
os.makedirs(lib_dir, exist_ok=True)

# 依赖文件列表
deps = {
    'bootstrap.min.css': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
    'bootstrap.bundle.min.js': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
    'socket.io.min.js': 'https://cdn.socket.io/4.5.4/socket.io.min.js'
}

# 下载依赖文件
for filename, url in deps.items():
    print(f'Downloading {filename}...')
    response = requests.get(url)
    if response.status_code == 200:
        with open(os.path.join(lib_dir, filename), 'wb') as f:
            f.write(response.content)
        print(f'Successfully downloaded {filename}')
    else:
        print(f'Failed to download {filename}') 