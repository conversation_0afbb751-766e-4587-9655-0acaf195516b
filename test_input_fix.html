<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输入法修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .input-area {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            resize: vertical;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .composing {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .not-composing {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-text {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-family: monospace;
            line-height: 1.6;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>输入法修复测试</h1>
    
    <div class="test-container">
        <h2>测试说明</h2>
        <p>这个测试页面用于验证输入法组合事件的处理是否正确。请按照以下步骤测试：</p>
        <ol>
            <li><strong>英文测试：</strong>在下面的输入框中输入英文，观察状态变化</li>
            <li><strong>中文测试：</strong>使用拼音输入法输入中文，注意在拼音状态下的行为</li>
            <li><strong>混合测试：</strong>输入中英文混合内容</li>
        </ol>
    </div>

    <div class="test-container">
        <h2>目标文本</h2>
        <div class="test-text">
            hello world 你好世界 welcome to typing game 欢迎来到打字游戏
        </div>
        
        <h3>输入区域</h3>
        <textarea id="testInput" class="input-area" placeholder="请在此输入上面的文本..."></textarea>
        
        <div id="status" class="status not-composing">
            状态：未在组合输入
        </div>
        
        <div>
            <strong>输入长度：</strong><span id="inputLength">0</span> | 
            <strong>目标长度：</strong><span id="targetLength">0</span> |
            <strong>是否完成：</strong><span id="isComplete">否</span>
        </div>
    </div>

    <div class="test-container">
        <h2>事件日志</h2>
        <div id="eventLog" class="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        const testInput = document.getElementById('testInput');
        const status = document.getElementById('status');
        const inputLength = document.getElementById('inputLength');
        const targetLength = document.getElementById('targetLength');
        const isComplete = document.getElementById('isComplete');
        const eventLog = document.getElementById('eventLog');
        
        const targetText = "hello world 你好世界 welcome to typing game 欢迎来到打字游戏";
        targetLength.textContent = targetText.length;
        
        let isComposing = false;
        
        function logEvent(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            eventLog.appendChild(logEntry);
            eventLog.scrollTop = eventLog.scrollHeight;
        }
        
        function updateStatus() {
            if (isComposing) {
                status.className = 'status composing';
                status.textContent = '状态：正在组合输入（拼音状态）';
            } else {
                status.className = 'status not-composing';
                status.textContent = '状态：未在组合输入';
            }
            
            const currentLength = testInput.value.length;
            inputLength.textContent = currentLength;
            isComplete.textContent = currentLength === targetText.length ? '是' : '否';
        }
        
        function checkCompletion() {
            if (!isComposing && testInput.value.length === targetText.length) {
                logEvent('✅ 检测到输入完成，可以安全跳转！');
                alert('输入完成！在实际游戏中，这里会自动跳转到下一行/页。');
            }
        }
        
        // 输入法组合事件
        testInput.addEventListener('compositionstart', (e) => {
            isComposing = true;
            updateStatus();
            logEvent(`🎯 组合开始: "${e.data || ''}"`);
        });
        
        testInput.addEventListener('compositionupdate', (e) => {
            logEvent(`📝 组合更新: "${e.data || ''}"`);
        });
        
        testInput.addEventListener('compositionend', (e) => {
            isComposing = false;
            updateStatus();
            logEvent(`✅ 组合结束: "${e.data || ''}"`);
            
            // 延迟检查完成状态，确保input事件已处理
            setTimeout(() => {
                checkCompletion();
            }, 0);
        });
        
        // 输入事件
        testInput.addEventListener('input', (e) => {
            updateStatus();
            logEvent(`⌨️ 输入事件: 长度=${testInput.value.length}, 组合状态=${isComposing}`);
            
            // 只有在非组合状态下才检查完成
            if (!isComposing) {
                checkCompletion();
            }
        });
        
        function clearLog() {
            eventLog.innerHTML = '';
        }
        
        // 初始化
        updateStatus();
        logEvent('🚀 测试页面已加载，开始测试...');
    </script>
</body>
</html>
