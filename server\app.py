from flask import Flask, render_template, request, jsonify, send_from_directory, send_file
from flask_socketio import SocketIO
import os
import json
import threading
import socket
import eventlet
import sys
from flask_compress import Compress  # 需要安装: pip install flask-compress

from models import Database
from socket_handler import SocketHandler

# 初始化Flask应用
app = Flask(__name__, static_folder='../client', static_url_path='')
app.config['SECRET_KEY'] = 'typing_contest_secret_key'

# 启用压缩
Compress(app)

# 设置缓存控制
@app.after_request
def add_header(response):
    # 静态资源缓存1小时
    if 'Cache-Control' not in response.headers:
        if request.path.startswith('/js/') or request.path.startswith('/css/') or request.path.startswith('/img/'):
            response.headers['Cache-Control'] = 'public, max-age=3600'
    return response

# 初始化SocketIO，减少ping超时时间以加快连接速度
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='eventlet',
                   ping_timeout=5, ping_interval=25)

# 初始化数据库
db = Database()

# 初始化Socket处理器
socket_handler = SocketHandler(socketio, db)

# 全局变量
current_contest = None
manager_connected = False

def get_local_ip():
    """获取本机IP地址"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def find_available_port(start_port=5000):
    """查找可用端口"""
    port = start_port
    while port < 65535:
        try:
            # 尝试创建一个测试socket
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.bind(('', port))
            test_socket.close()
            return port
        except OSError:
            port += 1
    return None

# 路由设置
@app.route('/')
def index():
    return send_from_directory('../client', 'index.html')

@app.route('/loading')
def loading_page():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="refresh" content="1;url=/">
        <title>加载中...</title>
        <style>
            body {
                background-color: #000;
                color: #fff;
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100vh;
                margin: 0;
                font-family: Arial, sans-serif;
            }
            .loader {
                border: 16px solid #333;
                border-top: 16px solid #3498db;
                border-radius: 50%;
                width: 120px;
                height: 120px;
                animation: spin 2s linear infinite;
                margin-bottom: 20px;
            }
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            .container {
                text-align: center;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="loader"></div>
            <h2>正在加载太空打字挑战赛...</h2>
        </div>
    </body>
    </html>
    """

@app.route('/api/contest/status')
def get_contest_status():
    contest = db.get_active_contest()
    # 确保响应使用UTF-8编码
    response = jsonify(contest if contest else {})
    response.headers['Content-Type'] = 'application/json; charset=utf-8'
    return response

@app.route('/api/users/online')
def get_online_users():
    users = db.get_all_online_users()
    return jsonify(users)

# 管理员API (需要在实际应用中添加认证)
@app.route('/api/admin/contest/create', methods=['POST'])
def create_contest():
    # 强制解析JSON并确保使用UTF-8编码
    data = request.get_json(force=True)
    text_content = data.get('text_content', '')

    if not text_content:
        response = jsonify({'error': 'Text content is required'})
        response.headers['Content-Type'] = 'application/json; charset=utf-8'
        return response, 400

    # 确保文本内容是UTF-8编码
    if isinstance(text_content, bytes):
        text_content = text_content.decode('utf-8')

    contest_id = db.create_contest(text_content)
    response = jsonify({'contest_id': contest_id, 'status': 'waiting'})
    response.headers['Content-Type'] = 'application/json; charset=utf-8'
    return response

@app.route('/api/admin/contest/start', methods=['POST'])
def start_contest():
    data = request.json
    contest_id = data.get('contest_id')

    if not contest_id:
        contest = db.get_active_contest()
        if contest:
            contest_id = contest['id']
        else:
            return jsonify({'error': 'No active contest found'}), 404

    db.start_contest(contest_id)
    socket_handler.broadcast_contest_status('running', contest_id)
    return jsonify({'status': 'success'})

@app.route('/api/admin/contest/pause', methods=['POST'])
def pause_contest():
    data = request.json
    contest_id = data.get('contest_id')

    if not contest_id:
        contest = db.get_active_contest()
        if contest:
            contest_id = contest['id']
        else:
            return jsonify({'error': 'No active contest found'}), 404

    db.pause_contest(contest_id)
    socket_handler.broadcast_contest_status('paused', contest_id)
    return jsonify({'status': 'success'})

@app.route('/api/admin/contest/end', methods=['POST'])
def end_contest():
    data = request.json
    contest_id = data.get('contest_id')

    if not contest_id:
        contest = db.get_active_contest()
        if contest:
            contest_id = contest['id']
        else:
            return jsonify({'error': 'No active contest found'}), 404

    db.end_contest(contest_id)
    socket_handler.broadcast_contest_status('ended', contest_id)
    return jsonify({'status': 'success'})

@app.route('/api/admin/user/kick', methods=['POST'])
def kick_user():
    data = request.json
    session_id = data.get('session_id')

    if not session_id:
        return jsonify({'error': 'Session ID is required'}), 400

    success = socket_handler.kick_user(session_id)
    return jsonify({'status': 'success' if success else 'failed'})

# 启动服务器
if __name__ == '__main__':
    host = get_local_ip()
    port = find_available_port(5000)

    if port is None:
        print("Error: No available ports found")
        sys.exit(1)

    print(f"Server running at http://{host}:{port}")
    try:
        # 使用eventlet优化
        eventlet.monkey_patch()
        socketio.run(app, host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"Error starting server: {str(e)}")
        sys.exit(1)