const nicknameGenerator = new NicknameGenerator();

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM内容已加载');

    // 获取DOM元素
    const nicknameInput = document.getElementById('nickname');
    const generateNicknameBtn = document.getElementById('generate-nickname');
    const startBtn = document.getElementById('start-btn');
    const loginScreen = document.getElementById('login-screen');
    const waitingScreen = document.getElementById('waiting-screen');

    console.log('DOM元素获取结果:', {
        nicknameInput: !!nicknameInput,
        generateNicknameBtn: !!generateNicknameBtn,
        startBtn: !!startBtn,
        loginScreen: !!loginScreen,
        waitingScreen: !!waitingScreen
    });

    // 生成昵称的函数
    function generateNewNickname() {
        const newNickname = nicknameGenerator.generate();
        nicknameInput.value = newNickname;
        return newNickname;
    }

    // 页面加载时立即生成一个默认昵称
    const defaultNickname = generateNewNickname();

    // 点击"换一个"按钮时生成新昵称
    generateNicknameBtn.addEventListener('click', generateNewNickname);

    // 开始按钮点击事件
    startBtn.addEventListener('click', function() {
        console.log('开始挑战按钮被点击');
        const currentNickname = nicknameInput.value;
        if (!currentNickname) {
            nicknameInput.value = generateNewNickname();
        }

        console.log('准备显示等待界面，昵称:', nicknameInput.value);
        // 显示等待界面
        loginScreen.classList.add('d-none');
        waitingScreen.classList.remove('d-none');

        // 确保socket已经初始化后再发送注册事件
        function tryRegister() {
            console.log('尝试注册用户，socket状态:', window.socket ? '存在' : '不存在',
                       window.socket && window.socket.connected ? '已连接' : '未连接');

            if (window.socket && window.socket.connected) {
                // Socket已连接，发送注册事件
                console.log('发送注册事件，昵称:', nicknameInput.value);
                window.socket.emit('register_user', {
                    nickname: nicknameInput.value
                });
            } else if (window.socket) {
                // Socket存在但未连接，等待连接
                console.log('Socket存在但未连接，等待连接...');
                window.socket.on('connect', function() {
                    console.log('Socket连接成功，发送注册事件');
                    window.socket.emit('register_user', {
                        nickname: nicknameInput.value
                    });
                });
            } else {
                // Socket还未初始化，等待一段时间后重试
                console.log('Socket未初始化，100ms后重试');
                setTimeout(tryRegister, 100);
            }
        }

        tryRegister();
    });

    // 确保昵称输入框始终有值
    nicknameInput.addEventListener('blur', function() {
        if (!this.value.trim()) {
            this.value = generateNewNickname();
        }
    });

    // 禁止用户手动编辑昵称
    nicknameInput.addEventListener('keydown', function(e) {
        e.preventDefault();
        return false;
    });

    // 添加到 DOMContentLoaded 事件处理中
    document.getElementById('restart-btn').addEventListener('click', function() {
        // 返回到等待界面
        document.getElementById('result-screen').classList.add('d-none');
        document.getElementById('waiting-screen').classList.remove('d-none');

        // 确保socket已经初始化后再发送注册事件
        function tryRestart() {
            if (window.socket && window.socket.connected) {
                // Socket已连接，发送注册事件
                window.socket.emit('register_user', {
                    nickname: document.getElementById('nickname').value
                });
            } else if (window.socket) {
                // Socket存在但未连接，等待连接
                window.socket.on('connect', function() {
                    window.socket.emit('register_user', {
                        nickname: document.getElementById('nickname').value
                    });
                });
            } else {
                // Socket还未初始化，等待一段时间后重试
                setTimeout(tryRestart, 100);
            }
        }

        tryRestart();
    });
});