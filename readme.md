# 在线打字比赛系统

一个支持多人在线参与的打字比赛平台，采用太空主题设计。

## 技术栈

### 服务端
- Python 3.10
- Flask (Web框架)
- SQLite(数据存储)
- Socket.IO (实时通信)
- Tkinter (管理界面)

### 客户端
- HTML5
- CSS3
- JavaScript
- Socket.IO-client
- Bootstrap (UI框架)

## 系统架构

### 服务端功能
1. 管理界面（Tkinter）
   - 显示服务器IP和端口
   - 比赛文本设置
   - 比赛控制（开始/暂停/结束）
   - 在线用户管理（查看/踢出）
   - 实时数据统计展示

2. Web服务器（Flask）
   - RESTful API接口
   - WebSocket实时通信
   - 用户会话管理
   - 成绩计算和排名
   - 并发连接处理

### 客户端功能
1. 用户界面
   - 太空主题设计
   - 随机昵称生成
   - 打字练习界面
   - 实时成绩显示
   - 排行榜显示

2. 核心功能
   - 实时打字检测
   - 成绩统计（正确率、速度、得分）
   - WebSocket通信
   - 错误提示
   - 实时排名更新

## 项目结构

项目名称：在线打字比赛
项目描述：
客户端：访问网址，界面显示"请输入昵称""开始练习"，界面美观，默认用户不可更改昵称，随机生成，但有更换按钮。进入界面后，显示打字练习界面，界面美观，逐行显示服务端设置的文字，练习者需在下一行输入正确的字符，并有反馈，实时统计正确率和正确个数和得分，得分以正确个数、正确率、错误个数处理计算得出。界面右侧实时显示前十名。
服务端：Python3.10 、tkinker界面，flask技术,界面可以显示客户端应访问网址(IP),设置文字后，点击开始，打字者可以开始比赛，否则客户端显示等待比赛开始中，可看到在线人的信息，IP,昵称，正确率和正确个数和得分，。服务端可以停止比赛，暂停比赛，将用户踢出，可以看到总共多少人在线。

要求：应能支持60个客户端不卡顿，客户端的风格要美观，颜色丰富可爱太空主题