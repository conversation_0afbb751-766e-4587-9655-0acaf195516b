<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>您的网站</title>
    <style>
        /* 加载指示器样式 */
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 隐藏主内容，直到加载完成 */
        #app-content {
            display: none;
        }
        
        /* 内容加载完成后应用的类 */
        .content-loaded #app-content {
            display: block;
        }
        
        .content-loaded #loading-overlay {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 加载指示器 -->
    <div id="loading-overlay">
        <div class="loading-spinner"></div>
        <p>加载中，请稍候...</p>
    </div>
    
    <!-- 主要内容区域 -->
    <div id="app-content">
        <!-- 您的网站内容 -->
    </div>
    
    <!-- 在页面底部引入脚本 -->
    <script src="main.js"></script>
</body>
</html> 