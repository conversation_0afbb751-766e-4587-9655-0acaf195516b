class NicknameGenerator {
    constructor() {
        // 更丰富的昵称组合
        this.prefixes = [
            // 自然
            "森林", "海洋", "山川", "河流", "天空", "云朵", "彩虹", "星星",
            // 动物
            "熊猫", "老虎", "狮子", "猫咪", "小兔", "兔子", "蝴蝶", "海豚",
            // 食物
            "蛋糕", "冰淇淋", "巧克力", "糖果", "果汁", "奶茶", "寿司",
            // 季节
            "春天", "夏天", "秋天", "冬天", "阳光", "雨滴", "雪花",
            // 职业
            "画家", "作家", "歌手", "舞者", "程序员", "医生", "老师",
            // 其他
            "勇士", "冒险家", "梦想家", "艺术家", "旅行者", "读者", "音乐家"
        ];
        
        this.suffixes = [
            "小天使", "达人", "高手", "专家", "新手", "爱好者", "玩家",
            "大师", "精灵", "守护者", "追梦人", "探索者", "收藏家", "创造者",
            "小能手", "迷", "控", "发烧友", "粉丝", "达人", "专家"
        ];
        
        // 英文字母组合
        this.letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    }

    // 生成随机数字
    generateNumber() {
        return Math.floor(Math.random() * 900) + 100;
    }

    // 生成随机字母
    generateLetters(length = 2) {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += this.letters.charAt(Math.floor(Math.random() * this.letters.length));
        }
        return result;
    }

    // 生成完整昵称
    generate() {
        const prefix = this.prefixes[Math.floor(Math.random() * this.prefixes.length)];
        const suffix = this.suffixes[Math.floor(Math.random() * this.suffixes.length)];
        const letters = this.generateLetters();
        const number = this.generateNumber();
        
        return `${prefix}${suffix}-${letters}${number}`;
    }
}

// 导出昵称生成器
window.NicknameGenerator = NicknameGenerator; 