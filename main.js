// 监听资源加载情况
document.addEventListener('DOMContentLoaded', function() {
    // 在这里可以初始化一些必要的内容
    
    // 假设我们需要加载一些关键资源
    const resourcesToLoad = [
        // 列出您的主要CSS和JS文件
        // 例如: '/css/styles.css', '/js/vendor.js', '/js/app.js'
    ];
    
    // 预加载所有关键资源
    Promise.all(resourcesToLoad.map(url => {
        return new Promise((resolve, reject) => {
            const isCSS = url.endsWith('.css');
            const isScript = url.endsWith('.js');
            
            let element;
            if (isCSS) {
                element = document.createElement('link');
                element.rel = 'stylesheet';
                element.href = url;
            } else if (isScript) {
                element = document.createElement('script');
                element.src = url;
                element.async = false; // 确保脚本按顺序执行
            } else {
                // 处理其他类型资源
                const img = new Image();
                img.src = url;
                img.onload = resolve;
                img.onerror = reject;
                return;
            }
            
            element.onload = resolve;
            element.onerror = reject;
            document.head.appendChild(element);
        });
    }))
    .then(() => {
        // 所有资源加载完成
        hideLoadingScreen();
    })
    .catch(error => {
        console.error('资源加载失败:', error);
        // 也可以显示加载失败的信息，但仍然显示页面内容
        hideLoadingScreen();
    });
});

// 隐藏加载屏幕，显示内容
function hideLoadingScreen() {
    // 等待所有DOM完全渲染并准备好
    setTimeout(() => {
        document.body.classList.add('content-loaded');
    }, 300); // 短暂延迟确保流畅过渡
}

// 添加页面加载完成事件
window.addEventListener('load', function() {
    // 如果到这里还没有隐藏加载屏幕，则隐藏它
    if (!document.body.classList.contains('content-loaded')) {
        hideLoadingScreen();
    }
}); 