import PyInstaller.__main__
import os
import shutil

# 清理旧的构建文件
for dir_name in ['dist', 'build']:
    if os.path.exists(dir_name):
        shutil.rmtree(dir_name)

# 打包配置
PyInstaller.__main__.run([
    'server/manager.py',                    # 主程序
    '--name=TypingContest',                 # 可执行文件名
    '--onedir',                             # 打包成一个目录
    '--noconsole',                          # 不显示控制台
    '--clean',                              # 清理临时文件
    '--add-data=client;client',             # 包含客户端文件夹(含lib子文件夹)
    '--add-data=server;server',             # 添加服务器文件
    '--collect-all=eventlet',
    '--collect-all=engineio',
    '--collect-all=socketio',
    '--collect-all=dns',
    '--collect-all=flask',
    '--collect-all=werkzeug',
    '--exclude-module=matplotlib',
    '--exclude-module=matplotlib_inline',
    '--hidden-import=engineio.async_drivers.eventlet',
    '--hidden-import=dns.resolver',
    '--hidden-import=eventlet.hubs.epolls',
    '--hidden-import=eventlet.hubs.kqueue',
    '--hidden-import=eventlet.hubs.selects',
    '--hidden-import=werkzeug.urls',
    '--hidden-import=werkzeug.utils',
    '--hidden-import=flask.cli',
    '--hidden-import=sqlite3',
])

# 复制必要的运行时文件
dist_dir = os.path.join('dist', 'TypingContest') 