import sqlite3
import os
import sys
import time
from datetime import datetime
import json

class Database:
    def __init__(self, db_path='typing_contest.db'):
        # 获取应用程序的根目录
        if getattr(sys, 'frozen', False):
            # 如果是打包后的可执行文件
            application_path = os.path.dirname(sys.executable)
        else:
            # 如果是开发环境
            application_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # 确保数据库文件在可写目录中
        self.db_path = os.path.join(application_path, db_path)
        self.init_db()

    def init_db(self):
        """初始化数据库表结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 创建用户表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nickname TEXT NOT NULL,
            ip TEXT NOT NULL,
            session_id TEXT UNIQUE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # 创建比赛记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS contests (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            start_time TIMESTAMP,
            end_time TIMESTAMP,
            text_content TEXT NOT NULL,
            status TEXT DEFAULT 'waiting'
        )
        ''')

        # 创建成绩表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS scores (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            contest_id INTEGER,
            correct_count INTEGER DEFAULT 0,
            error_count INTEGER DEFAULT 0,
            accuracy REAL DEFAULT 0,
            score INTEGER DEFAULT 0,
            completed BOOLEAN DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (contest_id) REFERENCES contests (id)
        )
        ''')

        # 创建设置表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS settings (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL
        )
        ''')

        conn.commit()
        conn.close()

    def add_user(self, nickname, ip, session_id):
        """添加新用户"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO users (nickname, ip, session_id) VALUES (?, ?, ?)",
            (nickname, ip, session_id)
        )
        user_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return user_id

    def get_user_by_session(self, session_id):
        """通过会话ID获取用户"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users WHERE session_id = ?", (session_id,))
        user = cursor.fetchone()
        conn.close()
        if user:
            return {
                'id': user[0],
                'nickname': user[1],
                'ip': user[2],
                'session_id': user[3],
                'created_at': user[4]
            }
        return None

    def create_contest(self, text_content):
        """创建新比赛"""
        # 确保文本内容是UTF-8编码的字符串
        if isinstance(text_content, bytes):
            text_content = text_content.decode('utf-8')

        conn = sqlite3.connect(self.db_path)
        # 设置数据库连接为UTF-8编码
        conn.text_factory = str
        cursor = conn.cursor()
        cursor.execute(
            "INSERT INTO contests (text_content, status) VALUES (?, 'waiting')",
            (text_content,)
        )
        contest_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return contest_id

    def start_contest(self, contest_id):
        """开始比赛"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE contests SET status = 'running', start_time = ? WHERE id = ?",
            (datetime.now().isoformat(), contest_id)
        )
        conn.commit()
        conn.close()

    def pause_contest(self, contest_id):
        """暂停比赛"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE contests SET status = 'paused' WHERE id = ?",
            (contest_id,)
        )
        conn.commit()
        conn.close()

    def end_contest(self, contest_id):
        """结束比赛"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute(
            "UPDATE contests SET status = 'ended', end_time = ? WHERE id = ?",
            (datetime.now().isoformat(), contest_id)
        )
        conn.commit()
        conn.close()

    def get_active_contest(self):
        """获取当前活动的比赛"""
        conn = sqlite3.connect(self.db_path)
        # 设置数据库连接为UTF-8编码
        conn.text_factory = str
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM contests WHERE status IN ('waiting', 'running', 'paused') ORDER BY id DESC LIMIT 1")
        contest = cursor.fetchone()
        conn.close()
        if contest:
            # 确保文本内容是UTF-8编码
            text_content = contest[3]
            if isinstance(text_content, bytes):
                text_content = text_content.decode('utf-8')

            return {
                'id': contest[0],
                'start_time': contest[1],
                'end_time': contest[2],
                'text_content': text_content,
                'status': contest[4]
            }
        return None

    def update_score(self, user_id, contest_id, correct_count, error_count, completed=False):
        """更新用户得分"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 计算准确率
            total_keystrokes = correct_count + error_count
            accuracy = (correct_count / total_keystrokes) * 100 if total_keystrokes > 0 else 0

            # 计算得分 - 每正确一个字符得2分，每错误一个字符扣1分，但最低为0分
            base_score = correct_count * 2
            penalty = error_count * 1
            final_score = max(0, base_score - penalty)  # 确保得分不低于0

            # 检查是否已有得分记录
            cursor.execute("""
                SELECT id FROM scores
                WHERE user_id = ? AND contest_id = ?
            """, (user_id, contest_id))

            score_record = cursor.fetchone()

            if score_record:
                # 更新现有记录
                cursor.execute("""
                    UPDATE scores
                    SET correct_count = ?, error_count = ?, accuracy = ?, score = ?, completed = ?
                    WHERE user_id = ? AND contest_id = ?
                """, (correct_count, error_count, accuracy, final_score, completed, user_id, contest_id))
            else:
                # 创建新记录
                cursor.execute("""
                    INSERT INTO scores (user_id, contest_id, correct_count, error_count, accuracy, score, completed)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (user_id, contest_id, correct_count, error_count, accuracy, final_score, completed))

            conn.commit()
        except Exception as e:
            print(f"Error updating score: {str(e)}")
        finally:
            conn.close()

    def get_leaderboard(self, contest_id):
        """获取比赛排行榜"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 获取配置的排行榜数量限制
        limit = self.get_leaderboard_limit()

        try:
            cursor.execute("""
                SELECT
                    u.id, u.nickname, u.session_id,
                    s.correct_count, s.error_count, s.accuracy, s.score, s.completed
                FROM
                    users u
                JOIN
                    scores s ON u.id = s.user_id
                WHERE
                    s.contest_id = ?
                ORDER BY
                    s.score DESC, s.accuracy DESC
                LIMIT ?
            """, (contest_id, limit))

            rows = cursor.fetchall()

            leaderboard = []
            for row in rows:
                leaderboard.append({
                    'user_id': row[0],
                    'nickname': row[1],
                    'session_id': row[2],
                    'correct_count': row[3],
                    'error_count': row[4],
                    'accuracy': row[5],
                    'score': row[6],
                    'completed': bool(row[7])
                })

            return leaderboard
        except Exception as e:
            print(f"获取排行榜失败: {str(e)}")
            return []
        finally:
            conn.close()

    def get_all_online_users(self):
        """获取所有在线用户"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            # 获取最新的比赛ID
            cursor.execute("SELECT id FROM contests ORDER BY id DESC LIMIT 1")
            contest_row = cursor.fetchone()
            if not contest_row:
                return []

            contest_id = contest_row[0]

            # 获取用户数据和成绩
            cursor.execute("""
                SELECT
                    u.*,
                    COALESCE(s.correct_count, 0) as correct_count,
                    COALESCE(s.error_count, 0) as error_count,
                    COALESCE(s.accuracy, 0.0) as accuracy,
                    COALESCE(s.score, 0) as score
                FROM users u
                LEFT JOIN scores s ON u.id = s.user_id AND s.contest_id = ?
                WHERE u.created_at > datetime('now', '-30 minutes')
                ORDER BY s.score DESC NULLS LAST
            """, (contest_id,))

            users = [dict(row) for row in cursor.fetchall()]
            conn.close()
            return users
        except Exception as e:
            print(f"Error getting online users: {str(e)}")
            conn.close()
            return []

    def remove_user(self, session_id):
        """移除用户"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute("DELETE FROM users WHERE session_id = ?", (session_id,))
        conn.commit()
        conn.close()

    def set_setting(self, key, value):
        """设置系统配置"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 插入或更新设置
            cursor.execute("""
                INSERT OR REPLACE INTO settings (key, value)
                VALUES (?, ?)
            """, (key, json.dumps(value)))

            conn.commit()
        except Exception as e:
            print(f"Error setting value: {str(e)}")
        finally:
            conn.close()

    def get_setting(self, key, default=None):
        """获取系统配置"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT value FROM settings WHERE key = ?", (key,))
            row = cursor.fetchone()

            if row:
                return json.loads(row[0])
            return default
        except Exception as e:
            print(f"Error getting setting: {str(e)}")
            return default
        finally:
            conn.close()

    def set_leaderboard_limit(self, limit=10):
        """设置排行榜显示数量"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # 将整数转换为JSON字符串存储
            value = json.dumps(limit)

            # 检查是否已有设置
            cursor.execute("SELECT value FROM settings WHERE key = 'leaderboard_limit'")
            existing = cursor.fetchone()

            if existing:
                cursor.execute(
                    "UPDATE settings SET value = ? WHERE key = 'leaderboard_limit'",
                    (value,)
                )
            else:
                cursor.execute(
                    "INSERT INTO settings (key, value) VALUES (?, ?)",
                    ('leaderboard_limit', value)
                )

            conn.commit()
            return True
        except Exception as e:
            print(f"设置排行榜显示数量失败: {str(e)}")
            return False
        finally:
            conn.close()

    def get_leaderboard_limit(self):
        """获取排行榜显示数量，默认为10"""
        default_limit = 10
        try:
            limit = self.get_setting('leaderboard_limit', default_limit)
            return int(limit)
        except:
            return default_limit